<script lang="ts">
	// TypeScript interfaces for PhotoSwipe
	interface PhotoSwipeItem {
		src: string
		w: number
		h: number
		title?: string
		msrc?: string
		el: Element
		pid?: string
	}

	interface PhotoSwipeOptions {
		galleryUID: string | null
		getThumbBoundsFn: (index: number) => { x: number; y: number; w: number }
		index?: number
		galleryPIDs?: boolean
		showAnimationDuration?: number
	}

	interface HashParams {
		gid?: number
		pid?: string
		[key: string]: string | number | undefined
	}

	const initPhotoSwipeFromDOM = (gallerySelector: string): void => {
		// parse slide data (url, title, size ...) from DOM elements
		// (children of gallerySelector)

		const parseThumbnailElements = (el: Element): PhotoSwipeItem[] => {
			const thumbElements = el.childNodes
			const numNodes = thumbElements.length
			const items: PhotoSwipeItem[] = []
			let figureEl: Element
			let linkEl: Element
			let size: string[]
			let item: PhotoSwipeItem

			for (let i = 0; i < numNodes; i++) {
				figureEl = thumbElements[i] as Element
				// <figure> element
				// include only element nodes
				if (figureEl.nodeType !== 1) {
					continue
				}
				linkEl = figureEl.children[0]
				// <a> element
				const dataSizeAttr = linkEl.getAttribute('data-size')
				if (!dataSizeAttr) continue

				size = dataSizeAttr.split('x')
				// create slide object
				const href = linkEl.getAttribute('href')
				if (!href) continue

				item = {
					src: href,
					w: parseInt(size[0], 10),
					h: parseInt(size[1], 10),
					el: figureEl
				}

				if (figureEl.children.length > 1) {
					// <figcaption> content
					item.title = figureEl.children[1].innerHTML
				}
				if (linkEl.children.length > 0) {
					// <img> thumbnail element, retrieving thumbnail url
					const src = (linkEl.children[0] as HTMLImageElement).getAttribute('src')
					if (src) {
						item.msrc = src
					}
				}
				// save link to element for getThumbBoundsFn
				items.push(item)
			}
			return items
		}

		// find nearest parent element
		const closest = (el: Element | null, fn: (el: Element) => boolean): Element | null => {
			return el && (fn(el) ? el : closest(el.parentNode as Element, fn))
		}

		// triggers when user clicks on thumbnail
		const onThumbnailsClick = (e: Event): boolean => {
			e.preventDefault()
			const eTarget = e.target as Element
			// find root element of slide
			const clickedListItem = closest(eTarget, (el: Element) => {
				return !!(el.tagName && el.tagName.toUpperCase() === 'FIGURE')
			})
			if (!clickedListItem) {
				return false
			}
			// find index of clicked item by looping through all child nodes
			// alternatively, you may define index via data- attribute
			const clickedGallery = clickedListItem.parentNode as Element
			const childNodes = clickedListItem.parentNode!.childNodes
			const numChildNodes = childNodes.length
			let nodeIndex = 0
			let index: number | undefined

			for (let i = 0; i < numChildNodes; i++) {
				if (childNodes[i].nodeType !== 1) {
					continue
				}
				if (childNodes[i] === clickedListItem) {
					index = nodeIndex
					break
				}
				nodeIndex++
			}
			if (index !== undefined && index >= 0) {
				// open PhotoSwipe if valid index found
				openPhotoSwipe(index, clickedGallery, false, false)
			}
			return false
		}

		// parse picture index and gallery index from URL (#&pid=1&gid=2)
		const photoswipeParseHash = (): HashParams => {
			const hash = window.location.hash.substring(1)
			const params: HashParams = {}
			if (hash.length < 5) {
				return params
			}
			const vars = hash.split('&')
			for (let i = 0; i < vars.length; i++) {
				if (!vars[i]) {
					continue
				}
				const pair = vars[i].split('=')
				if (pair.length < 2) {
					continue
				}
				params[pair[0]] = pair[1]
			}
			if (params.gid && typeof params.gid === 'string') {
				params.gid = parseInt(params.gid, 10)
			}
			return params
		}

		const openPhotoSwipe = (
			index: number | string,
			galleryElement: Element,
			disableAnimation: boolean,
			fromURL: boolean
		): void => {
			const pswpElement = document.querySelectorAll('.pswp')[0]
			let gallery: any
			const items = parseThumbnailElements(galleryElement)

			// define options (if needed)
			const options: PhotoSwipeOptions = {
				galleryUID: galleryElement.getAttribute('data-pswp-uid'),
				getThumbBoundsFn: (index: number) => {
					// See Options -> getThumbBoundsFn section of documentation for more info
					const thumbnail = items[index].el.getElementsByTagName('img')[0]
					const pageYScroll = window.pageYOffset || document.documentElement.scrollTop
					const rect = thumbnail.getBoundingClientRect()
					return {
						x: rect.left,
						y: rect.top + pageYScroll,
						w: rect.width
					}
				}
			}

			// PhotoSwipe opened from URL
			if (fromURL) {
				if (options.galleryPIDs) {
					// parse real index when custom PIDs are used
					// http://photoswipe.com/documentation/faq.html#custom-pid-in-url
					for (let j = 0; j < items.length; j++) {
						if (items[j].pid === index) {
							options.index = j
							break
						}
					}
				} else {
					// in URL indexes start from 1
					options.index = parseInt(index as string, 10) - 1
				}
			} else {
				options.index = parseInt(index as string, 10)
			}
			// exit if index not found
			if (isNaN(options.index!)) {
				return
			}
			if (disableAnimation) {
				options.showAnimationDuration = 0
			}
			// Pass data to PhotoSwipe and initialize it
			gallery = new (window as any).PhotoSwipe(
				pswpElement,
				(window as any).PhotoSwipeUI_Default,
				items,
				options
			)
			gallery.init()
		}

		// loop through all gallery elements and bind events
		const galleryElements = document.querySelectorAll(gallerySelector)
		for (let i = 0; i < galleryElements.length; i++) {
			galleryElements[i].setAttribute('data-pswp-uid', (i + 1).toString())
			;(galleryElements[i] as HTMLElement).onclick = onThumbnailsClick
		}
		// Parse URL and open gallery if it contains #&pid=3&gid=1
		const hashData = photoswipeParseHash()
		if (hashData.pid && hashData.gid) {
			openPhotoSwipe(hashData.pid, galleryElements[hashData.gid - 1], true, true)
		}
	}

	// Placeholder function for SVG placeholders
	const placeholder = (width: number, height: number): string => {
		return `data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http://www.w3.org/2000/svg'%20viewBox%3D'0%200%20${width}%20${height}'%20%2F%3E`
	}

	// Gallery images data
	const images = [
		{ id: '-gS54SWrHMg', width: 1000, height: 500, caption: '1000x500' },
		{ id: 'AU1rKyKPJco', width: 500, height: 1000, caption: '500x1000' },
		{ id: 'AXfDvKOawZQ' },
		{ id: 'gKlkZrsG_Pw', width: 1000, height: 500, caption: '1000x500' },
		{ id: 'DVONaLgCRxo', width: 500, height: 1000, caption: '500x1000' },
		{ id: 'o7txpYpxNLs', caption: 'Have a nice day' },
		{
			id: 'ZsgUsl8GATg',
			width: 500,
			height: 1000,
			caption: '500x1000'
		},
		{ id: 'CkagyZJ88kE' },
		{ id: 'PpQ4-HOZ_8U', width: 1000, height: 500, caption: '1000x500' },
		{ id: 'si7gjqJQj_8' },
		{ id: 'u0M0gyuexfE', width: 500, height: 1000, caption: '500x1000' },
		{ id: 'aQcE3gDSSTY' },
		{ id: 'GkCafprWKRo', width: 500, height: 1000, caption: '500x1000' },
		{ id: 'OFlzoTfpRdw' },
		{ id: 'YlFM0-LdHu8' },
		{ id: 'c_Tc9ZELeYw' }
	]

	// Helper function to get image class based on dimensions
	const getImageClass = (width: number = 500, height: number = 500): string => {
		if (height > width) return 'vertical'
		if (width > height) return 'horizontal'
		return ''
	}

	// execute above function
	initPhotoSwipeFromDOM('.gallery')
</script>

<!-- Gallery HTML markup -->
<div class="gallery" itemscope itemtype="http://schema.org/ImageGallery">
	{#each images as image}
		{@const width = image.width || 500}
		{@const height = image.height || 500}
		{@const imageClass = getImageClass(width, height)}
		<figure
			class="gallery-item {imageClass}"
			itemprop="associatedMedia"
			itemscope
			itemtype="http://schema.org/ImageObject"
		>
			<a
				href="https://source.unsplash.com/{image.id}/{width * 2}x{height * 2}"
				itemprop="contentUrl"
				data-size="{width * 2}x{height * 2}"
			>
				<!-- <img
					class="lazyload lazypreload fadein"
					src={placeholder(width, height)}
					data-src="https://source.unsplash.com/{image.id}/{width}x{height}"
					itemprop="thumbnail"
					alt="Image description"
				/> -->
				<img
					class="lazyload lazypreload fadein"
					src="./hammers06.jpg"
					data-src="{image.id}/{width}x{height}"
					itemprop="thumbnail"
					alt="Image description"
				/>
			</a>
			<figcaption class="gallery-caption" itemprop="caption description">
				{image.caption || 'Caption'}
			</figcaption>
		</figure>
	{/each}
</div>

<!-- PhotoSwipe markup -->
<!-- Root element of PhotoSwipe. Must have class pswp. -->
<div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
	<!-- Background of PhotoSwipe. It's a separate element as animating opacity is faster than rgba(). -->
	<div class="pswp__bg"></div>

	<!-- Slides wrapper with overflow:hidden. -->
	<div class="pswp__scroll-wrap">
		<!-- Container that holds slides. PhotoSwipe keeps only 3 of them in the DOM to save memory. Don't modify these 3 pswp__item elements, data is added later on. -->
		<div class="pswp__container">
			<div class="pswp__item"></div>
			<div class="pswp__item"></div>
			<div class="pswp__item"></div>
		</div>

		<!-- Default (PhotoSwipeUI_Default) interface on top of sliding area. Can be changed. -->
		<div class="pswp__ui pswp__ui--hidden">
			<div class="pswp__top-bar">
				<!-- Controls are self-explanatory. Order can be changed. -->
				<div class="pswp__counter"></div>
				<button class="pswp__button pswp__button--close" title="Close (Esc)"></button>
				<button class="pswp__button pswp__button--share" title="Share"></button>
				<button class="pswp__button pswp__button--fs" title="Toggle fullscreen"></button>
				<button class="pswp__button pswp__button--zoom" title="Zoom in/out"></button>

				<!-- Preloader demo http://codepen.io/dimsemenov/pen/yyBWoR -->
				<!-- element will get class pswp__preloader--active when preloader is running -->
				<div class="pswp__preloader">
					<div class="pswp__preloader__icn">
						<div class="pswp__preloader__cut">
							<div class="pswp__preloader__donut"></div>
						</div>
					</div>
				</div>
			</div>

			<div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
				<div class="pswp__share-tooltip"></div>
			</div>

			<button class="pswp__button pswp__button--arrow--left" title="Previous (arrow left)"></button>
			<button class="pswp__button pswp__button--arrow--right" title="Next (arrow right)"></button>

			<div class="pswp__caption">
				<div class="pswp__caption__center"></div>
			</div>
		</div>
	</div>
</div>

<style>
	/* Gallery Caption Styles */
	.gallery-caption {
		position: absolute;
		bottom: 4rem;
		left: 50%;
		transform: translate(-50%, 0%);
		font-size: 12px;
		background: red;
		color: rgba(255, 255, 255, 0);
		padding: 1.25em 1.5em;
		transition: all 0.2s ease;
		font-weight: 600;
		line-height: 1.25;
		text-align: center;
		box-sizing: border-box;
		pointer-events: none;
	}

	@media (min-width: 768px) {
		.gallery-caption {
			font-size: 14px;
		}
	}

	.gallery-caption:before,
	.gallery-caption:after {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 1);
		width: 100%;
		height: 100%;
		transition: all 0.3s ease 0s;
		z-index: -1;
	}

	.gallery-caption:before {
		top: auto;
		height: 3px;
		transform: scale(0, 1);
		transform-origin: bottom left;
		transition-delay: 0.6s;
	}

	.gallery-caption:after {
		transform: scale(1, 0);
		transform-origin: bottom;
		transition-delay: 0.3s;
	}

	.gallery-caption.visible {
		color: rgba(255, 255, 255, 1);
		text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
		transition: all 0.3s ease 0.3s;
	}

	.gallery-caption.visible:before {
		transform: scale(1, 1);
		transition-delay: 0s;
	}

	.gallery-caption.visible:after {
		transform: scale(1, 1);
	}

	.gallery-caption:empty {
		display: none;
	}

	/* Image Border Styles */
	.gallery-item a {
		position: relative;
	}

	.gallery-item a:before,
	.gallery-item a:after {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		border: 0 solid rgba(0, 0, 0, 0.1);
		transition: all 0.2s;
		will-change: border;
		z-index: 10;
	}

	.gallery-item a.active:before {
		border-width: 0.5rem;
	}

	.gallery-item a.active:after {
		border-width: 2px;
	}

	.gallery-item a:after {
		margin: 1rem;
		border: 2px solid rgba(255, 255, 255, 0.5);
		clip-path: polygon(
			0 calc(100% - 1rem),
			0 100%,
			1rem 100%,
			1rem 0,
			0 0,
			0 1rem,
			100% 1rem,
			100% 0,
			calc(100% - 1rem) 0,
			calc(100% - 1rem) 100%,
			100% 100%,
			100% calc(100% - 1rem)
		);
	}

	.gallery-item a:hover:after {
		transform: scale(0.9);
		border-color: rgba(255, 255, 255, 1);
	}

	/* Caption Outside Styles */
	.gallery-caption.outside {
		background-color: black;
		color: white;
		padding: 0.75em 1em;
		display: inline-block;
		text-align: left;
	}

	/* Gallery Grid Styles */
	.gallery {
		font-family:
			-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
		width: 100%;
		display: grid;
		grid-template-rows: flow;
		grid-auto-flow: dense;
	}

	@media (max-width: 767px) {
		.gallery {
			grid-template-columns: repeat(2, 1fr);
		}
	}

	@media (min-width: 768px) and (max-width: 1023px) {
		.gallery {
			grid-template-columns: repeat(3, 1fr);
		}
	}

	@media (min-width: 1024px) and (max-width: 1279px) {
		.gallery {
			grid-template-columns: repeat(4, 1fr);
		}
	}

	@media (min-width: 1280px) and (max-width: 1599px) {
		.gallery {
			grid-template-columns: repeat(5, 1fr);
		}
	}

	@media (min-width: 1600px) {
		.gallery {
			grid-template-columns: repeat(6, 1fr);
		}
	}

	/* Gallery Item Styles */
	.gallery-item {
		position: relative;
		background-color: rgba(0, 0, 0, 0.5);
		overflow: hidden;
	}

	.gallery-item img,
	.gallery-item a {
		display: block;
	}

	.gallery-item img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		object-position: center;
	}

	.gallery-item a {
		width: 100%;
		height: 100%;
		min-height: 200px;
	}

	.gallery-item.vertical {
		grid-row: span 2;
	}

	.gallery-item.horizontal {
		grid-column: span 2;
	}

	.gallery-item a {
		display: block;
	}

	/* Lazy loading styles */
	.lazy-images .gallery-item a.image-lazyloaded:before,
	html:not(.lazy-images) .gallery-item a:before {
		border-width: 0.5rem;
	}

	.lazy-images .gallery-item a.image-lazyloaded:after,
	html:not(.lazy-images) .gallery-item a:after {
		border-width: 2px;
	}

	/* Caption visibility for non-touch devices and hover states */

	.gallery-item:hover .gallery-caption {
		color: rgba(255, 255, 255, 1);
		text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
		transition: all 0.3s ease 0.3s;
	}

	.gallery-item:hover .gallery-caption:before {
		transform: scale(1, 1);
		transition-delay: 0s;
	}

	.gallery-item:hover .gallery-caption:after {
		transform: scale(1, 1);
	}

	/* Hide captions in list views */
	[class*='list'] .gallery-caption,
	.gallery-size-thumbnail .gallery-caption {
		display: none;
	}

	.pswp {
		display: none;
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		overflow: hidden;
		touch-action: none;
		z-index: 1500;
		-webkit-text-size-adjust: 100%;
		backface-visibility: hidden;
		outline: none;
	}

	.pswp * {
		box-sizing: border-box;
	}

	.pswp img {
		max-width: none;
	}

	.pswp--open {
		display: block;
	}

	.pswp__bg {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background: #000;
		opacity: 0;
		transform: translateZ(0);
		backface-visibility: hidden;
		will-change: opacity;
	}

	.pswp__scroll-wrap {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		overflow: hidden;
	}

	.pswp__container,
	.pswp__zoom-wrap {
		touch-action: none;
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
	}

	.pswp__container,
	.pswp__img {
		user-select: none;
		-webkit-tap-highlight-color: transparent;
		-webkit-touch-callout: none;
	}

	.pswp__item {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		overflow: hidden;
	}

	.pswp__img {
		position: absolute;
		width: auto;
		height: auto;
		top: 0;
		left: 0;
	}
</style>
